/********************************************************************************
** Form generated from reading UI file 'ChatDialog.ui'
**
** Created by: Qt User Interface Compiler version 5.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_CHATDIALOG_H
#define UI_CHATDIALOG_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_Form
{
public:
    QHBoxLayout *horizontalLayout;
    QWidget *wigeSideBar;
    QVBoxLayout *verticalLayout_2;
    QLabel *lbUser;
    QSpacerItem *verticalSpacer_2;
    QWidget *widget_2;
    QVBoxLayout *verticalLayout_3;
    QLabel *lbMsg;
    QLabel *lbLinkman;
    QSpacerItem *verticalSpacer;
    QWidget *wigeChatUser;
    QVBoxLayout *verticalLayout;
    QWidget *widget;
    QHBoxLayout *horizontalLayout_2;
    QLineEdit *lineEdit;
    QSpacerItem *horizontalSpacer;
    QPushButton *btnAdd;
    QListWidget *listwigeUswe;
    QWidget *wigeCharData;
    QVBoxLayout *verticalLayout_4;
    QWidget *wigeTitle;
    QHBoxLayout *horizontalLayout_3;
    QWidget *widget_6;
    QHBoxLayout *horizontalLayout_4;
    QLabel *lbTitle;
    QWidget *widget_7;
    QWidget *widget_3;
    QWidget *wigeTool;
    QHBoxLayout *horizontalLayout_6;
    QSpacerItem *horizontalSpacer_2;
    QLabel *lbEmo;
    QSpacerItem *horizontalSpacer_4;
    QLabel *lbJietu;
    QSpacerItem *horizontalSpacer_5;
    QLabel *lbFile;
    QSpacerItem *horizontalSpacer_3;
    QWidget *wigeText;
    QHBoxLayout *horizontalLayout_5;
    QTextEdit *textEdit;
    QWidget *widget_4;
    QHBoxLayout *horizontalLayout_7;
    QSpacerItem *horizontalSpacer_6;
    QPushButton *pushButton_2;

    void setupUi(QWidget *Form)
    {
        if (Form->objectName().isEmpty())
            Form->setObjectName(QStringLiteral("Form"));
        Form->resize(1032, 910);
        horizontalLayout = new QHBoxLayout(Form);
        horizontalLayout->setSpacing(0);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        horizontalLayout->setContentsMargins(0, 0, 0, 0);
        wigeSideBar = new QWidget(Form);
        wigeSideBar->setObjectName(QStringLiteral("wigeSideBar"));
        QSizePolicy sizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(wigeSideBar->sizePolicy().hasHeightForWidth());
        wigeSideBar->setSizePolicy(sizePolicy);
        wigeSideBar->setMinimumSize(QSize(56, 0));
        wigeSideBar->setMaximumSize(QSize(56, 16777215));
        verticalLayout_2 = new QVBoxLayout(wigeSideBar);
        verticalLayout_2->setSpacing(7);
        verticalLayout_2->setObjectName(QStringLiteral("verticalLayout_2"));
        verticalLayout_2->setContentsMargins(15, 15, 10, 0);
        lbUser = new QLabel(wigeSideBar);
        lbUser->setObjectName(QStringLiteral("lbUser"));
        lbUser->setMinimumSize(QSize(40, 40));
        lbUser->setMaximumSize(QSize(40, 40));

        verticalLayout_2->addWidget(lbUser);

        verticalSpacer_2 = new QSpacerItem(40, 5, QSizePolicy::Minimum, QSizePolicy::Fixed);

        verticalLayout_2->addItem(verticalSpacer_2);

        widget_2 = new QWidget(wigeSideBar);
        widget_2->setObjectName(QStringLiteral("widget_2"));
        widget_2->setMinimumSize(QSize(0, 0));
        widget_2->setMaximumSize(QSize(30, 16777215));
        widget_2->setSizeIncrement(QSize(0, 0));
        verticalLayout_3 = new QVBoxLayout(widget_2);
        verticalLayout_3->setSpacing(30);
        verticalLayout_3->setObjectName(QStringLiteral("verticalLayout_3"));
        verticalLayout_3->setContentsMargins(0, 0, 0, 0);
        lbMsg = new QLabel(widget_2);
        lbMsg->setObjectName(QStringLiteral("lbMsg"));
        lbMsg->setMinimumSize(QSize(30, 30));
        lbMsg->setMaximumSize(QSize(30, 30));

        verticalLayout_3->addWidget(lbMsg);

        lbLinkman = new QLabel(widget_2);
        lbLinkman->setObjectName(QStringLiteral("lbLinkman"));
        lbLinkman->setMinimumSize(QSize(30, 30));
        lbLinkman->setMaximumSize(QSize(30, 30));

        verticalLayout_3->addWidget(lbLinkman);


        verticalLayout_2->addWidget(widget_2);

        verticalSpacer = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_2->addItem(verticalSpacer);


        horizontalLayout->addWidget(wigeSideBar);

        wigeChatUser = new QWidget(Form);
        wigeChatUser->setObjectName(QStringLiteral("wigeChatUser"));
        wigeChatUser->setMaximumSize(QSize(250, 16777215));
        verticalLayout = new QVBoxLayout(wigeChatUser);
        verticalLayout->setSpacing(0);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        verticalLayout->setContentsMargins(0, 0, 0, 0);
        widget = new QWidget(wigeChatUser);
        widget->setObjectName(QStringLiteral("widget"));
        widget->setMinimumSize(QSize(0, 60));
        widget->setMaximumSize(QSize(16777215, 60));
        horizontalLayout_2 = new QHBoxLayout(widget);
        horizontalLayout_2->setSpacing(7);
        horizontalLayout_2->setObjectName(QStringLiteral("horizontalLayout_2"));
        horizontalLayout_2->setContentsMargins(7, 0, 6, 1);
        lineEdit = new QLineEdit(widget);
        lineEdit->setObjectName(QStringLiteral("lineEdit"));

        horizontalLayout_2->addWidget(lineEdit);

        horizontalSpacer = new QSpacerItem(5, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);

        btnAdd = new QPushButton(widget);
        btnAdd->setObjectName(QStringLiteral("btnAdd"));
        btnAdd->setMinimumSize(QSize(24, 24));
        btnAdd->setMaximumSize(QSize(24, 24));

        horizontalLayout_2->addWidget(btnAdd);


        verticalLayout->addWidget(widget);

        listwigeUswe = new QListWidget(wigeChatUser);
        listwigeUswe->setObjectName(QStringLiteral("listwigeUswe"));

        verticalLayout->addWidget(listwigeUswe);


        horizontalLayout->addWidget(wigeChatUser);

        wigeCharData = new QWidget(Form);
        wigeCharData->setObjectName(QStringLiteral("wigeCharData"));
        verticalLayout_4 = new QVBoxLayout(wigeCharData);
        verticalLayout_4->setSpacing(0);
        verticalLayout_4->setObjectName(QStringLiteral("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(0, 0, 0, 0);
        wigeTitle = new QWidget(wigeCharData);
        wigeTitle->setObjectName(QStringLiteral("wigeTitle"));
        wigeTitle->setMinimumSize(QSize(0, 60));
        wigeTitle->setMaximumSize(QSize(16777215, 60));
        horizontalLayout_3 = new QHBoxLayout(wigeTitle);
        horizontalLayout_3->setObjectName(QStringLiteral("horizontalLayout_3"));
        horizontalLayout_3->setContentsMargins(0, 0, 0, 0);
        widget_6 = new QWidget(wigeTitle);
        widget_6->setObjectName(QStringLiteral("widget_6"));
        horizontalLayout_4 = new QHBoxLayout(widget_6);
        horizontalLayout_4->setObjectName(QStringLiteral("horizontalLayout_4"));
        lbTitle = new QLabel(widget_6);
        lbTitle->setObjectName(QStringLiteral("lbTitle"));

        horizontalLayout_4->addWidget(lbTitle);


        horizontalLayout_3->addWidget(widget_6);

        widget_7 = new QWidget(wigeTitle);
        widget_7->setObjectName(QStringLiteral("widget_7"));

        horizontalLayout_3->addWidget(widget_7);


        verticalLayout_4->addWidget(wigeTitle);

        widget_3 = new QWidget(wigeCharData);
        widget_3->setObjectName(QStringLiteral("widget_3"));

        verticalLayout_4->addWidget(widget_3);

        wigeTool = new QWidget(wigeCharData);
        wigeTool->setObjectName(QStringLiteral("wigeTool"));
        wigeTool->setMinimumSize(QSize(0, 60));
        wigeTool->setMaximumSize(QSize(16777215, 60));
        horizontalLayout_6 = new QHBoxLayout(wigeTool);
        horizontalLayout_6->setSpacing(0);
        horizontalLayout_6->setObjectName(QStringLiteral("horizontalLayout_6"));
        horizontalLayout_6->setContentsMargins(0, 0, 0, 0);
        horizontalSpacer_2 = new QSpacerItem(5, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_2);

        lbEmo = new QLabel(wigeTool);
        lbEmo->setObjectName(QStringLiteral("lbEmo"));
        lbEmo->setMinimumSize(QSize(25, 25));
        lbEmo->setMaximumSize(QSize(25, 25));

        horizontalLayout_6->addWidget(lbEmo);

        horizontalSpacer_4 = new QSpacerItem(5, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_4);

        lbJietu = new QLabel(wigeTool);
        lbJietu->setObjectName(QStringLiteral("lbJietu"));
        lbJietu->setMinimumSize(QSize(25, 25));
        lbJietu->setMaximumSize(QSize(25, 25));

        horizontalLayout_6->addWidget(lbJietu);

        horizontalSpacer_5 = new QSpacerItem(5, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_5);

        lbFile = new QLabel(wigeTool);
        lbFile->setObjectName(QStringLiteral("lbFile"));
        lbFile->setMinimumSize(QSize(25, 25));
        lbFile->setMaximumSize(QSize(25, 25));

        horizontalLayout_6->addWidget(lbFile);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_6->addItem(horizontalSpacer_3);


        verticalLayout_4->addWidget(wigeTool);

        wigeText = new QWidget(wigeCharData);
        wigeText->setObjectName(QStringLiteral("wigeText"));
        wigeText->setMinimumSize(QSize(0, 150));
        wigeText->setMaximumSize(QSize(16777215, 150));
        horizontalLayout_5 = new QHBoxLayout(wigeText);
        horizontalLayout_5->setSpacing(0);
        horizontalLayout_5->setObjectName(QStringLiteral("horizontalLayout_5"));
        horizontalLayout_5->setContentsMargins(0, 0, 0, 0);
        textEdit = new QTextEdit(wigeText);
        textEdit->setObjectName(QStringLiteral("textEdit"));
        textEdit->setMinimumSize(QSize(0, 0));

        horizontalLayout_5->addWidget(textEdit);


        verticalLayout_4->addWidget(wigeText);

        widget_4 = new QWidget(wigeCharData);
        widget_4->setObjectName(QStringLiteral("widget_4"));
        widget_4->setMinimumSize(QSize(0, 60));
        widget_4->setMaximumSize(QSize(16777215, 60));
        horizontalLayout_7 = new QHBoxLayout(widget_4);
        horizontalLayout_7->setObjectName(QStringLiteral("horizontalLayout_7"));
        horizontalSpacer_6 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_7->addItem(horizontalSpacer_6);

        pushButton_2 = new QPushButton(widget_4);
        pushButton_2->setObjectName(QStringLiteral("pushButton_2"));
        pushButton_2->setMinimumSize(QSize(100, 30));
        pushButton_2->setMaximumSize(QSize(100, 30));

        horizontalLayout_7->addWidget(pushButton_2);


        verticalLayout_4->addWidget(widget_4);

        widget_3->raise();
        widget_4->raise();
        wigeText->raise();
        wigeTitle->raise();
        wigeTool->raise();

        horizontalLayout->addWidget(wigeCharData);


        retranslateUi(Form);

        QMetaObject::connectSlotsByName(Form);
    } // setupUi

    void retranslateUi(QWidget *Form)
    {
        Form->setWindowTitle(QApplication::translate("Form", "Form", Q_NULLPTR));
        lbUser->setText(QString());
        lbMsg->setText(QString());
        lbLinkman->setText(QString());
        btnAdd->setText(QString());
        lbTitle->setText(QApplication::translate("Form", "\350\212\261\346\236\234\345\261\261\351\207\216\347\214\264", Q_NULLPTR));
        lbEmo->setText(QString());
        lbJietu->setText(QString());
        lbFile->setText(QString());
        pushButton_2->setText(QApplication::translate("Form", "\345\217\221\351\200\201", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class Form: public Ui_Form {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_CHATDIALOG_H
