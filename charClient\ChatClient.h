#pragma once
#include <QObject>
#include <QThread>
#include <QTimer>
#include "public/Logger.h"
#include "public/UdpSocket.h"
#include "public/ChatProtocol.h"
#include <iostream>

// 网络接收工作类
class NetworkWorker : public QObject
{
    Q_OBJECT

public:
    NetworkWorker(UdpSocket* socket, QObject* parent = nullptr);
    ~NetworkWorker();

public slots:
    void startReceiving();
    void stopReceiving();

private slots:
    void receiveData();

signals:
    void dataReceived(QByteArray data, QString fromAddress);
    void errorOccurred(QString error);

private:
    UdpSocket* m_udpSocket;
    QTimer* m_timer;
    bool m_isRunning;
};


/**
 * @class ChatClient
 * @brief 聊天客户端类，继承自QObject，用于处理聊天相关的网络通信
 */
class ChatClient : public QObject
{
    Q_OBJECT  // Qt元对象系统，用于信号槽机制
public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针，默认为nullptr
     */
    ChatClient(QObject* parent = nullptr);
    /**
     * @brief 析构函数
     */
    ~ChatClient();

    /**
     * @brief 启动聊天客户端
     * @param name 用户名
     */
    void run(std::string name);
    /**
     * @brief 停止聊天客户端
     */
    void stop();

    /**
     * @brief 发送登录请求
     * @param name 用户名
     */
    void sendLogin(const std::string& name);
    /**
     * @brief 发送登出请求
     * @param name 用户名
     */
    void sendLogout(const std::string& name);
    /**
     * @brief 发送文本消息
     * @param msg 要发送的消息内容
     */
    void sendTextMessage(const std::string& msg);
    void sendPacket(uint16_t cmd, const void* data, int len);


private slots:
    void onDataReceived(QByteArray data, QString fromAddress);
    void onNetworkError(QString error);

private:
    //处理包
    void handlePacket(void* data, int len);
    void handleLoginResponse(MsgHeader* header, void* buf);
private:
    UdpSocket m_udpSocket;
    SocketAddress m_addr;

    // Qt多线程相关
    QThread* m_networkThread;
    NetworkWorker* m_networkWorker;

public slots:

signals:
    void loginSuccess();
    void loginFailed(QString reason);

};