#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UDP测试服务器，用于测试Qt聊天客户端的登录功能
"""

import socket
import struct
import threading
import time

# 协议定义
MSG_PROTOCOL_VERSION_1_0 = 0x0100
CMD_LOGIN = 0
CMD_LOGIN_RESPONSE = 1
CMD_LOGOUT = 2
CMD_TEXT_MSG = 3

class TestServer:
    def __init__(self, host='127.0.0.1', port=5678):
        self.host = host
        self.port = port
        self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        self.running = False
        self.clients = {}  # 存储已登录的客户端
        
    def start(self):
        """启动服务器"""
        try:
            self.socket.bind((self.host, self.port))
            self.running = True
            print(f"测试服务器启动在 {self.host}:{self.port}")
            
            while self.running:
                try:
                    data, addr = self.socket.recvfrom(1400)
                    if len(data) >= 6:  # 至少包含消息头
                        self.handle_packet(data, addr)
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"处理数据包时出错: {e}")
                    
        except Exception as e:
            print(f"服务器启动失败: {e}")
        finally:
            self.socket.close()
            
    def stop(self):
        """停止服务器"""
        self.running = False
        
    def handle_packet(self, data, addr):
        """处理收到的数据包"""
        try:
            # 解析消息头 (version:2, command:2, len:2)
            if len(data) < 6:
                print(f"数据包太短: {len(data)} 字节")
                return
                
            version, command, length = struct.unpack('<HHH', data[:6])
            payload = data[6:]
            
            print(f"收到来自 {addr} 的数据包:")
            print(f"  版本: 0x{version:04x}")
            print(f"  命令: {command}")
            print(f"  长度: {length}")
            
            if version != MSG_PROTOCOL_VERSION_1_0:
                print(f"不支持的协议版本: 0x{version:04x}")
                return
                
            if command == CMD_LOGIN:
                self.handle_login(payload, addr)
            elif command == CMD_LOGOUT:
                self.handle_logout(payload, addr)
            elif command == CMD_TEXT_MSG:
                self.handle_text_message(payload, addr)
            else:
                print(f"未知命令: {command}")
                
        except Exception as e:
            print(f"解析数据包时出错: {e}")
            
    def handle_login(self, payload, addr):
        """处理登录请求"""
        try:
            if len(payload) < 32:
                print("登录请求数据不足")
                return
                
            # 解析用户名 (32字节，C字符串)
            username_bytes = payload[:32]
            username = username_bytes.split(b'\x00')[0].decode('utf-8', errors='ignore')
            
            print(f"用户 '{username}' 尝试从 {addr} 登录")
            
            # 检查用户名是否已存在
            result_code = 0  # 0=成功, 1=用户名已存在
            if username in self.clients:
                result_code = 1
                print(f"用户名 '{username}' 已存在")
            else:
                self.clients[username] = addr
                print(f"用户 '{username}' 登录成功")
            
            # 发送登录响应
            self.send_login_response(addr, result_code)
            
        except Exception as e:
            print(f"处理登录请求时出错: {e}")
            
    def handle_logout(self, payload, addr):
        """处理登出请求"""
        try:
            if len(payload) < 32:
                print("登出请求数据不足")
                return
                
            username_bytes = payload[:32]
            username = username_bytes.split(b'\x00')[0].decode('utf-8', errors='ignore')
            
            print(f"用户 '{username}' 从 {addr} 登出")
            
            if username in self.clients:
                del self.clients[username]
                print(f"用户 '{username}' 已登出")
            
        except Exception as e:
            print(f"处理登出请求时出错: {e}")
            
    def handle_text_message(self, payload, addr):
        """处理文本消息"""
        try:
            if len(payload) < 1024:
                print("文本消息数据不足")
                return
                
            message_bytes = payload[:1024]
            message = message_bytes.split(b'\x00')[0].decode('utf-8', errors='ignore')
            
            print(f"收到来自 {addr} 的消息: '{message}'")
            
        except Exception as e:
            print(f"处理文本消息时出错: {e}")
            
    def send_login_response(self, addr, result_code):
        """发送登录响应"""
        try:
            # 构造响应包
            version = MSG_PROTOCOL_VERSION_1_0
            command = CMD_LOGIN_RESPONSE
            length = 2  # result_code 占2字节
            
            # 打包消息头和数据
            header = struct.pack('<HHH', version, command, length)
            payload = struct.pack('<H', result_code)
            
            response = header + payload
            
            self.socket.sendto(response, addr)
            print(f"发送登录响应到 {addr}, 结果码: {result_code}")
            
        except Exception as e:
            print(f"发送登录响应时出错: {e}")

def main():
    """主函数"""
    server = TestServer()
    
    try:
        print("启动UDP测试服务器...")
        print("按 Ctrl+C 停止服务器")
        server.start()
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        server.stop()
        print("服务器已停止")

if __name__ == "__main__":
    main()
