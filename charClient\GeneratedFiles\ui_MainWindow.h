/********************************************************************************
** Form generated from reading UI file 'MainWindow.ui'
**
** Created by: Qt User Interface Compiler version 5.6.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindowClass
{
public:
    QWidget *centralWidget;
    QHBoxLayout *horizontalLayout;
    QLabel *label;
    QLineEdit *ldUserName;
    QPushButton *btLogIn;

    void setupUi(QMainWindow *MainWindowClass)
    {
        if (MainWindowClass->objectName().isEmpty())
            MainWindowClass->setObjectName(QStringLiteral("MainWindowClass"));
        MainWindowClass->resize(317, 213);
        MainWindowClass->setStyleSheet(QString::fromUtf8("/* ======================== \345\205\250\345\261\200\345\255\227\344\275\223 ======================== */\n"
"QWidget {\n"
"    font-family: \"Microsoft YaHei\";\n"
"    font-size: 14px;\n"
"    color: #333333;\n"
"    background-color: #f7f7f7;\n"
"}\n"
"\n"
"/* ======================== QLabel ======================== */\n"
"QLabel {\n"
"    color: #444444;\n"
"    font-size: 14px;\n"
"}\n"
"\n"
"/* ======================== QLineEdit ======================== */\n"
"QLineEdit {\n"
"    border: 2px solid #cccccc;\n"
"    border-radius: 6px;\n"
"    padding: 6px 8px;\n"
"    background-color: #ffffff;\n"
"    selection-background-color: #0078d7;\n"
"    selection-color: white;\n"
"}\n"
"\n"
"QLineEdit:focus {\n"
"    border: 2px solid #0078d7;\n"
"    background-color: #ffffff;\n"
"}\n"
"\n"
"/* ======================== QPushButton ======================== */\n"
"QPushButton {\n"
"    background-color: #0078d7;\n"
"    color: white;\n"
"    border-radius: 8px;\n"
"    padding: 8px 16px;\n"
"    font-size: 14px;\n"
""
                        "    font-weight: bold;\n"
"    border: none;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #005a9e;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: #003f6d;\n"
"}\n"
""));
        centralWidget = new QWidget(MainWindowClass);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        horizontalLayout = new QHBoxLayout(centralWidget);
        horizontalLayout->setSpacing(6);
        horizontalLayout->setContentsMargins(11, 11, 11, 11);
        horizontalLayout->setObjectName(QStringLiteral("horizontalLayout"));
        label = new QLabel(centralWidget);
        label->setObjectName(QStringLiteral("label"));

        horizontalLayout->addWidget(label);

        ldUserName = new QLineEdit(centralWidget);
        ldUserName->setObjectName(QStringLiteral("ldUserName"));

        horizontalLayout->addWidget(ldUserName);

        btLogIn = new QPushButton(centralWidget);
        btLogIn->setObjectName(QStringLiteral("btLogIn"));

        horizontalLayout->addWidget(btLogIn);

        MainWindowClass->setCentralWidget(centralWidget);

        retranslateUi(MainWindowClass);

        QMetaObject::connectSlotsByName(MainWindowClass);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindowClass)
    {
        MainWindowClass->setWindowTitle(QApplication::translate("MainWindowClass", "\347\231\273\345\275\225", Q_NULLPTR));
        label->setText(QApplication::translate("MainWindowClass", "\347\224\250\346\210\267\345\220\215\357\274\232", Q_NULLPTR));
        btLogIn->setText(QApplication::translate("MainWindowClass", "\347\231\273\345\275\225", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class MainWindowClass: public Ui_MainWindowClass {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
