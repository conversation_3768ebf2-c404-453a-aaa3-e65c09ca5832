#include "MainWindow.h"


MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent), ui(new Ui::MainWindowClass)
{
    ui->setupUi(this);
    ui->ldUserName->setMaxLength(30);//限制最大输入

    m_pclient = new ChatClient;

    connect(ui->btLogIn, &QPushButton::clicked, this, &MainWindow::onButtonClicked);
    
    connect(m_pclient, &ChatClient::loginSuccess, this, [this]() {
        m_pCharDlg = new ChatDialog();
        setCentralWidget(m_pCharDlg);
        m_pCharDlg->show();
        this->hide();
        }, Qt::QueuedConnection);

}


void MainWindow::onButtonClicked() {
    LOG_DEBUG("MainWindow::onButtonClicked()");

    //读取用户输入是否正确
    std::string input = ui->ldUserName->text().toStdString();
    if (!input.empty()) {
        try {      
            m_pclient->run(input);
        }
        catch (SocketException& e) {
            delete m_pclient;
            LOG_FATAL(e.what()); 
        }
    }
}