#include "MainWindow.h"


MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent), ui(new Ui::MainWindowClass)
{
    ui->setupUi(this);
    ui->ldUserName->setMaxLength(30);//限制最大输入

    m_pclient = new ChatClient;

    connect(ui->btLogIn, &QPushButton::clicked, this, &MainWindow::onButtonClicked);
    
    connect(m_pclient, &ChatClient::loginSuccess, this, [this]() {
        LOG_INFO("MainWindow - 登录成功，切换到聊天界面");
        m_pCharDlg = new ChatDialog();
        setCentralWidget(m_pCharDlg);
        m_pCharDlg->show();
        this->hide();
        }, Qt::QueuedConnection);

    connect(m_pclient, &ChatClient::loginFailed, this, [this](QString reason) {
        LOG_ERROR("MainWindow - 登录失败: %s", reason.toStdString().c_str());
        // 这里可以显示错误消息给用户
        ui->btLogIn->setText("登录失败，重试");
        ui->btLogIn->setEnabled(true);
        }, Qt::QueuedConnection);

}


void MainWindow::onButtonClicked() {
    LOG_DEBUG("MainWindow::onButtonClicked() - 用户点击登录按钮");

    //读取用户输入是否正确
    std::string input = ui->ldUserName->text().toStdString();
    if (!input.empty()) {
        // 禁用登录按钮，防止重复点击
        ui->btLogIn->setEnabled(false);
        ui->btLogIn->setText("登录中...");

        try {
            m_pclient->run(input);
            LOG_INFO("MainWindow::onButtonClicked - 开始登录流程，用户名: %s", input.c_str());
        }
        catch (SocketException& e) {
            LOG_FATAL("MainWindow::onButtonClicked - 登录异常: %s", e.what());
            ui->btLogIn->setText("登录失败");
            ui->btLogIn->setEnabled(true);
        }
    }
    else {
        LOG_WARNING("MainWindow::onButtonClicked - 用户名为空");
    }
}