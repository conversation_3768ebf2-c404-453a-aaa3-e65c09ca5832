#include "ChatClient.h"

// NetworkWorker 实现
NetworkWorker::NetworkWorker(UdpSocket* socket, QObject* parent)
    : QObject(parent), m_udpSocket(socket), m_isRunning(false)
{
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &NetworkWorker::receiveData);
}

NetworkWorker::~NetworkWorker()
{
    stopReceiving();
}

void NetworkWorker::startReceiving()
{
    if (!m_isRunning) {
        m_isRunning = true;
        m_timer->start(10); // 每10ms检查一次是否有数据
        LOG_INFO("NetworkWorker::startReceiving - 开始接收网络数据");
    }
}

void NetworkWorker::stopReceiving()
{
    if (m_isRunning) {
        m_isRunning = false;
        m_timer->stop();
        LOG_INFO("NetworkWorker::stopReceiving - 停止接收网络数据");
    }
}

void NetworkWorker::receiveData()
{
    if (!m_isRunning || !m_udpSocket) {
        return;
    }

    try {
        char buf[MAX_PACKET_SIZE];
        SocketAddress addr;

        // 使用非阻塞接收
        int bytes = m_udpSocket->RecvFrom(addr, buf, sizeof(buf));
        if (bytes > 0) {
            QByteArray data(buf, bytes);
            QString fromAddress = QString::fromStdString(addr.toString());
            LOG_DEBUG("NetworkWorker::receiveData - 收到数据 %d 字节，来自 %s",
                     bytes, fromAddress.toStdString().c_str());
            emit dataReceived(data, fromAddress);
        }
        // bytes == 0 表示没有数据可读，这在非阻塞模式下是正常的
    }
    catch (SocketException& e) {
        // 真正的网络错误
        LOG_ERROR("NetworkWorker::receiveData - 网络错误: %s", e.what());
        emit errorOccurred(QString::fromStdString(e.what()));
    }
}

// ChatClient 实现
ChatClient::ChatClient(QObject* parent)
    : QObject(parent), m_addr(SERVER_IP, SERVER_PORT), m_networkThread(nullptr), m_networkWorker(nullptr)
{
    //初始化日志
    Logger::Config config;
    Logger::GetInstance().Init(config);

    LOG_INFO("ChatClient::ChatClient - 聊天客户端初始化完成");
}

ChatClient::~ChatClient()
{
    stop();
    LOG_INFO("ChatClient::~ChatClient - 聊天客户端销毁");
}

void ChatClient::run(std::string name)
{
    LOG_DEBUG("ChatClient::run - 开始运行，用户名: %s", name.c_str());

    try {
        // 发送登录包
        sendLogin(name);

        // 创建网络工作线程
        m_networkThread = new QThread(this);
        m_networkWorker = new NetworkWorker(&m_udpSocket);

        // 将worker移动到线程中
        m_networkWorker->moveToThread(m_networkThread);

        // 连接信号槽
        connect(m_networkThread, &QThread::started, m_networkWorker, &NetworkWorker::startReceiving);
        connect(m_networkThread, &QThread::finished, m_networkWorker, &NetworkWorker::stopReceiving);
        connect(m_networkWorker, &NetworkWorker::dataReceived, this, &ChatClient::onDataReceived);
        connect(m_networkWorker, &NetworkWorker::errorOccurred, this, &ChatClient::onNetworkError);

        // 启动线程
        m_networkThread->start();

        LOG_INFO("ChatClient::run - 网络接收线程已启动");
    }
    catch (SocketException& e) {
        LOG_FATAL("ChatClient::run - 启动失败: %s", e.what());
        emit loginFailed(QString::fromStdString(e.what()));
    }
}

void ChatClient::stop()
{
    LOG_DEBUG("ChatClient::stop - 停止聊天客户端");

    if (m_networkThread && m_networkThread->isRunning()) {
        // 停止线程
        m_networkThread->quit();
        m_networkThread->wait(3000); // 等待最多3秒

        if (m_networkThread->isRunning()) {
            LOG_WARNING("ChatClient::stop - 强制终止网络线程");
            m_networkThread->terminate();
            m_networkThread->wait(1000);
        }

        // 清理资源
        if (m_networkWorker) {
            m_networkWorker->deleteLater();
            m_networkWorker = nullptr;
        }

        m_networkThread->deleteLater();
        m_networkThread = nullptr;

        LOG_INFO("ChatClient::stop - 网络线程已停止");
    }
}

void ChatClient::sendLogin(const std::string& name)
{
    LOG_DEBUG("ChatClient::sendLogin - 发送登录请求，用户名: %s", name.c_str());
    LoginRequest request;
    strncpy(request.name, name.c_str(), sizeof(request.name));
    sendPacket(CMD_LOGIN, &request, sizeof(request));
}

void ChatClient::sendLogout(const std::string& name)
{
    LOG_DEBUG("ChatClient::sendLogout - 发送登出请求，用户名: %s", name.c_str());
    LoginRequest request;
    strncpy(request.name, name.c_str(), sizeof(request.name));
    sendPacket(CMD_LOGOUT, &request, sizeof(request));
}

void ChatClient::sendTextMessage(const std::string& msg)
{
    LOG_DEBUG("ChatClient::sendTextMessage - 发送文本消息: %s", msg.c_str());
    TextMessage request;
    strncpy(request.msg, msg.c_str(), sizeof(request.msg));
    sendPacket(CMD_TEXT_MSG, &request, sizeof(request));
}

void ChatClient::sendPacket(uint16_t cmd, const void* data, int len)
{
    char buf[MAX_PACKET_SIZE];
    MsgHeader* header = (MsgHeader*)buf;
    header->version = MSG_PROTOCOL_VERVER_1_0;
    header->command = cmd;
    header->len = len;
    int bufSize = sizeof(MsgHeader) + len;
    memcpy(buf + sizeof(MsgHeader), data, len);

    try {
        int bytes = m_udpSocket.SendTo(m_addr, buf, bufSize);
        LOG_DEBUG("ChatClient::sendPacket - 发送数据到 %s，字节数: %d，版本: %04x，命令: %d，长度: %d",
                 m_addr.toString().c_str(), bytes, header->version, header->command, header->len);
    }
    catch (SocketException& e) {
        LOG_ERROR("ChatClient::sendPacket - 发送失败: %s", e.what());
        emit loginFailed(QString::fromStdString(e.what()));
    }
}

void ChatClient::onDataReceived(QByteArray data, QString fromAddress)
{
    LOG_DEBUG("ChatClient::onDataReceived - 收到数据，来自: %s，大小: %d",
             fromAddress.toStdString().c_str(), data.size());

    // 将QByteArray转换为原始数据进行处理
    handlePacket(data.data(), data.size());
}

void ChatClient::onNetworkError(QString error)
{
    LOG_ERROR("ChatClient::onNetworkError - 网络错误: %s", error.toStdString().c_str());
    emit loginFailed(error);
}

void ChatClient::handlePacket(void* data, int len)
{
    if (len < sizeof(MsgHeader)) {
        LOG_ERROR("ChatClient::handlePacket - 数据包长度不足，小于消息头大小");
        return;
    }

    MsgHeader* header = (MsgHeader*)data;

    //检测协议版本
    if (header->version != MSG_PROTOCOL_VERVER_1_0) {
        LOG_ERROR("ChatClient::handlePacket - 协议版本错误: %d", header->version);
        return;
    }

    LOG_DEBUG("ChatClient::handlePacket - 处理数据包，版本: %04x，命令: %d，长度: %d",
             header->version, header->command, header->len);

    switch (header->command) {
    case CMD_LOGIN_RESPONSE:
        handleLoginResponse(header, header + 1);
        break;
    default:
        LOG_WARNING("ChatClient::handlePacket - 未知命令类型: %d", header->command);
        break;
    }
}

void ChatClient::handleLoginResponse(MsgHeader* header, void* buf)
{
    if (header->len != sizeof(LoginResponse)) {
        LOG_ERROR("ChatClient::handleLoginResponse - 登录响应长度错误 %d != %d",
                 header->len, sizeof(LoginResponse));
        return;
    }

    LoginResponse* data = (LoginResponse*)buf;
    if (data->resultCode == 0) {
        LOG_INFO("ChatClient::handleLoginResponse - 登录成功");
        emit loginSuccess();
    }
    else if (data->resultCode == 1) {
        LOG_WARNING("ChatClient::handleLoginResponse - 用户名已存在");
        emit loginFailed("用户名已存在");
    }
    else {
        LOG_ERROR("ChatClient::handleLoginResponse - 登录失败，错误代码: %d", data->resultCode);
        emit loginFailed(QString("登录失败，错误代码: %1").arg(data->resultCode));
    }
}
