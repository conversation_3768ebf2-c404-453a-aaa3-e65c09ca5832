#pragma once

#include <stdio.h>
#include <stdexcept>

class SocketException :public std::runtime_error {
public:
    SocketException(std::string msg) :runtime_error(msg + GetErrorMsg()) {

    }

    std::string GetErrorMsg() {
        LPVOID lpMsgBuf;
        DWORD errCode = WSAGetLastError();
        FormatMessageA(
            FORMAT_MESSAGE_ALLOCATE_BUFFER |
            FORMAT_MESSAGE_FROM_SYSTEM |
            FORMAT_MESSAGE_IGNORE_INSERTS,
            NULL,
            errCode,
            MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
            (LPSTR)&lpMsgBuf,
            0,
            NULL
        );
        int bytes = _snprintf(nullptr, 0, " ErrCode:%d ErrMsg:%s", errCode, (char*)lpMsgBuf);
        std::vector<char> vec(bytes + 1);
        _snprintf(vec.data(), vec.size(), " ErrCode:%d ErrMsg:%s", errCode, (char*)lpMsgBuf);
        LocalFree(lpMsgBuf);
        return vec.data();
    }
};