#pragma once
#include "SocketAddress.h"

class UdpSocket
{
public:
    UdpSocket() {
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
        m_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
        if (INVALID_SOCKET == m_socket) {
            throw SocketException("UdpSocket::UdpSocket Init socket");
        }
    }
    ~UdpSocket() {
        if (INVALID_SOCKET != m_socket) {
            closesocket(m_socket);
        }
        WSACleanup();
    }

    void Bind(const SocketAddress& addr) {
        if (bind(m_socket, addr, sizeof(addr)) < 0) {
            throw SocketException("UdpSocket::Bind");
        }
        LOG_DEBUG("UdpSocket::Bind %s", addr.toString().c_str());
    }

    int RecvFrom(SocketAddress& addr, char * buffer, int bufSize) {
        int len = addr.GetAddrSize();
        int bytes = recvfrom(m_socket, buffer, bufSize, 0, addr.GetSockAddr(), &len);
        if (bytes <= 0) {
            throw SocketException("UdpSocket::RecvFrom");
        }
        LOG_DEBUG("UdpSocket::RecvFrom bytes:%d", bytes);
        return bytes;
    }

    int SendTo(const SocketAddress& addr, const char* buffer, int bufSize) {
        int sentBytes = sendto(m_socket, buffer, bufSize, 0, addr.GetSockAddr(), addr.GetAddrSize());
        if (sentBytes <= 0) {
            throw SocketException("UdpSocket::SendTo");
        }
        return sentBytes;
    }
private:
    SOCKET m_socket;
};

