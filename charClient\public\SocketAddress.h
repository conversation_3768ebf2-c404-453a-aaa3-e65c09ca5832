#pragma once

#define _WINSOCK_DEPRECATED_NO_WARNINGS

#include <Winsock2.h>
#include <string>
#include <Ws2tcpip.h>
#include "Logger.h"
#include "Exception.h"

#pragma comment(lib, "Ws2_32.lib")


class SocketAddress
{
public:
    SocketAddress() {
        m_addr.sin_family = AF_INET;
        m_addr.sin_addr.S_un.S_addr = INADDR_ANY;
        m_addr.sin_port = 0;
        LOG_DEBUG("SocketAddress %s", toString().c_str());
    }
    SocketAddress(const std::string ip, unsigned short port) {
        m_addr.sin_family = AF_INET;
        m_addr.sin_addr.S_un.S_addr = inet_addr(ip.c_str());
        m_addr.sin_port = htons(port);
        LOG_DEBUG("SocketAddress %s", toString().c_str());
    }
    SocketAddress(const std::string host) { //www.51asm.com:5566
        LOG_DEBUG("SocketAddress host:%s", host);
        int index = host.find(":");
        if (index < 0) {
            throw SocketException("CSocketAddress Invalid Format");
        }
        LOG_DEBUG("SocketAddress index:%d", index);

        std::string subHostName = host.substr(0, index);
        std::string subPort = host.substr(index + 1, host.length());
        LOG_DEBUG("SocketAddress subHostName:%s subPort:%s", subHostName.c_str(), subPort.c_str());

        struct addrinfo* result = NULL;
        struct addrinfo hints;
        ZeroMemory(&hints, sizeof(hints));
        hints.ai_family = AF_INET;
        int ret = getaddrinfo(subHostName.c_str(), NULL, &hints, &result);
        if (ret != 0) {
            throw SocketException("SocketAddress getaddrinfo");
        }

        sockaddr_in* sockaddr = (struct sockaddr_in*)result->ai_addr;

        m_addr.sin_family = AF_INET;
        m_addr.sin_port = htons(atoi(subPort.c_str()));
        m_addr.sin_addr = sockaddr->sin_addr;
        freeaddrinfo(result);

        LOG_DEBUG("SocketAddress %s", toString());
    }
    virtual ~SocketAddress() {

    }
    virtual sockaddr* GetSockAddr() const {
        return (sockaddr*)&m_addr;
    }
    virtual operator sockaddr* () const {
        return (sockaddr*)&m_addr;
    }
    virtual int GetAddrSize() const {
        return sizeof(m_addr);
    }
    virtual bool operator == (const SocketAddress& addr)  const {
        return m_addr.sin_family == addr.m_addr.sin_family &&
            m_addr.sin_addr.S_un.S_addr == addr.m_addr.sin_addr.S_un.S_addr &&
            m_addr.sin_port == addr.m_addr.sin_port;
    }

    virtual std::string toString() const {
        char buf[1024];
        sprintf_s(buf, sizeof(buf), "%s:%d", inet_ntoa(m_addr.sin_addr), htons(m_addr.sin_port));
        return buf;
    }
private:
    sockaddr_in m_addr;
};

