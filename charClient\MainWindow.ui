<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindowClass</class>
 <widget class="QMainWindow" name="MainWindowClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>317</width>
    <height>213</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>登录</string>
  </property>
  <property name="styleSheet">
   <string notr="true">/* ======================== 全局字体 ======================== */
QWidget {
    font-family: &quot;Microsoft YaHei&quot;;
    font-size: 14px;
    color: #333333;
    background-color: #f7f7f7;
}

/* ======================== QLabel ======================== */
QLabel {
    color: #444444;
    font-size: 14px;
}

/* ======================== QLineEdit ======================== */
QLineEdit {
    border: 2px solid #cccccc;
    border-radius: 6px;
    padding: 6px 8px;
    background-color: #ffffff;
    selection-background-color: #0078d7;
    selection-color: white;
}

QLineEdit:focus {
    border: 2px solid #0078d7;
    background-color: #ffffff;
}

/* ======================== QPushButton ======================== */
QPushButton {
    background-color: #0078d7;
    color: white;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: bold;
    border: none;
}

QPushButton:hover {
    background-color: #005a9e;
}

QPushButton:pressed {
    background-color: #003f6d;
}
</string>
  </property>
  <widget class="QWidget" name="centralWidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="text">
       <string>用户名：</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="ldUserName"/>
    </item>
    <item>
     <widget class="QPushButton" name="btLogIn">
      <property name="text">
       <string>登录</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="MainWindow.qrc"/>
 </resources>
 <connections/>
</ui>
