# Qt多线程改造说明

## 改造概述

本项目将原来使用`std::thread`的多线程方式改造为Qt推荐的多线程模式，主要改进如下：

### 1. 原始设计问题
- 使用`std::thread`创建线程
- 静态函数作为线程入口点
- 阻塞式网络接收
- 线程管理复杂

### 2. Qt多线程改造方案

#### 2.1 使用QThread + moveToThread模式
```cpp
// 创建工作线程和工作对象
m_networkThread = new QThread(this);
m_networkWorker = new NetworkWorker(&m_udpSocket);

// 将工作对象移动到线程中
m_networkWorker->moveToThread(m_networkThread);

// 连接信号槽
connect(m_networkThread, &QThread::started, m_networkWorker, &NetworkWorker::startReceiving);
connect(m_networkThread, &QThread::finished, m_networkWorker, &NetworkWorker::stopReceiving);
```

#### 2.2 NetworkWorker类设计
```cpp
class NetworkWorker : public QObject
{
    Q_OBJECT
public:
    NetworkWorker(UdpSocket* socket, QObject* parent = nullptr);
    
public slots:
    void startReceiving();    // 开始接收数据
    void stopReceiving();     // 停止接收数据
    
private slots:
    void receiveData();       // 定时检查并接收数据
    
signals:
    void dataReceived(QByteArray data, QString fromAddress);
    void errorOccurred(QString error);
    
private:
    UdpSocket* m_udpSocket;
    QTimer* m_timer;          // 使用定时器进行非阻塞接收
    bool m_isRunning;
};
```

## 3. 主要改进点

### 3.1 非阻塞网络接收
- 修改UdpSocket为非阻塞模式
- 使用QTimer定时检查数据
- 避免线程阻塞

### 3.2 信号槽通信
- 线程间通过Qt信号槽通信
- 自动处理线程安全
- 支持跨线程调用

### 3.3 资源管理
- 使用Qt对象树管理内存
- 优雅的线程停止机制
- 自动清理资源

## 4. 登录流程演示

### 4.1 启动登录流程
```cpp
void ChatClient::run(std::string name)
{
    // 1. 发送登录包
    sendLogin(name);
    
    // 2. 创建网络工作线程
    m_networkThread = new QThread(this);
    m_networkWorker = new NetworkWorker(&m_udpSocket);
    
    // 3. 移动到线程并连接信号
    m_networkWorker->moveToThread(m_networkThread);
    connect(m_networkThread, &QThread::started, m_networkWorker, &NetworkWorker::startReceiving);
    connect(m_networkWorker, &NetworkWorker::dataReceived, this, &ChatClient::onDataReceived);
    
    // 4. 启动线程
    m_networkThread->start();
}
```

### 4.2 处理登录响应
```cpp
void ChatClient::handleLoginResponse(MsgHeader* header, void* buf)
{
    LoginResponse* data = (LoginResponse*)buf;
    if (data->resultCode == 0) {
        LOG_INFO("登录成功");
        emit loginSuccess();  // 发送成功信号
    }
    else {
        LOG_WARNING("登录失败，错误代码: %d", data->resultCode);
        emit loginFailed(QString("登录失败，错误代码: %1").arg(data->resultCode));
    }
}
```

### 4.3 UI响应
```cpp
// MainWindow中连接信号槽
connect(m_pclient, &ChatClient::loginSuccess, this, [this]() {
    LOG_INFO("登录成功，切换到聊天界面");
    m_pCharDlg = new ChatDialog();
    setCentralWidget(m_pCharDlg);
    m_pCharDlg->show();
    this->hide();
}, Qt::QueuedConnection);

connect(m_pclient, &ChatClient::loginFailed, this, [this](QString reason) {
    LOG_ERROR("登录失败: %s", reason.toStdString().c_str());
    ui->btLogIn->setText("登录失败，重试");
    ui->btLogIn->setEnabled(true);
}, Qt::QueuedConnection);
```

## 5. Qt多线程优势

### 5.1 线程安全
- Qt信号槽机制自动处理线程安全
- 跨线程调用自动排队
- 避免竞态条件

### 5.2 资源管理
- 使用Qt对象树自动管理内存
- 线程结束时自动清理资源
- 避免内存泄漏

### 5.3 调试友好
- Qt Creator提供线程调试工具
- 信号槽连接可视化
- 更好的错误信息

### 5.4 平台兼容
- Qt处理平台差异
- 统一的API接口
- 更好的可移植性

## 6. 使用建议

### 6.1 线程创建
- 优先使用moveToThread模式
- 避免继承QThread
- 工作对象继承QObject

### 6.2 通信方式
- 使用信号槽进行线程间通信
- 避免直接访问跨线程对象
- 使用Qt::QueuedConnection确保线程安全

### 6.3 资源清理
- 在析构函数中停止线程
- 使用deleteLater()延迟删除
- 等待线程结束后再清理

## 7. 编译和运行

确保项目配置包含以下Qt模块：
- QtCore
- QtWidgets
- QtNetwork (如果需要)

编译时Qt的moc会自动处理Q_OBJECT宏。
